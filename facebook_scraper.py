#!/usr/bin/env python3
"""
Facebook Post Scraper
Scrapes Facebook posts and extracts metadata including timestamps, text content, and other information.
Based on the LinkedIn scraper but adapted for Facebook posts.
"""

import re
import requests
import csv
import time
import pandas as pd
from urllib.parse import urlparse, parse_qs
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FacebookPostScraper:
    def __init__(self):
        self.setup_driver()
        
    def setup_driver(self):
        """Set up Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Run in background
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def extract_post_id_from_url(self, url):
        """Extract post ID from Facebook URL"""
        try:
            # Facebook post URLs can have various formats:
            # https://www.facebook.com/PageName/posts/pfbid...
            # https://www.facebook.com/photo.php?fbid=...
            if 'pfbid' in url:
                pattern = r'pfbid([a-zA-Z0-9]+)'
                match = re.search(pattern, url)
                if match:
                    return f"pfbid{match.group(1)}"
            elif 'fbid=' in url:
                pattern = r'fbid=(\d+)'
                match = re.search(pattern, url)
                if match:
                    return match.group(1)
            else:
                # Try to extract any numeric ID from the URL
                pattern = r'/(\d+)/?'
                matches = re.findall(pattern, url)
                if matches:
                    return matches[-1]  # Return the last numeric ID found
                    
            logger.warning(f"Could not extract post ID from URL: {url}")
            return None
        except Exception as e:
            logger.error(f"Error extracting post ID from {url}: {e}")
            return None
    
    def scrape_post_content(self, url):
        """Scrape the actual post content from Facebook"""
        try:
            self.driver.get(url)
            time.sleep(5)  # Facebook needs more time to load
            
            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            post_data = {
                'text': '',
                'author': '',
                'timestamp': '',
                'likes': '',
                'comments': '',
                'shares': '',
                'hashtags': []
            }
            
            # Try to extract post text
            try:
                # Multiple selectors to try for Facebook post content
                text_selectors = [
                    "[data-ad-preview='message']",
                    "[data-testid='post_message']",
                    ".userContent",
                    ".text_exposed_root",
                    "[data-testid='post-text']",
                    ".x1iorvi4.x1pi30zi.x1l90r2v.x1swvt13",  # Facebook's dynamic classes
                    "div[dir='auto']",
                    ".x11i5rnm.xat24cr.x1mh8g0r.x1vvkbs"
                ]
                
                for selector in text_selectors:
                    try:
                        text_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in text_elements:
                            text = element.text.strip()
                            if text and len(text) > 10:  # Filter out very short text
                                post_data['text'] = text
                                break
                        if post_data['text']:
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not extract post text: {e}")
            
            # Try to extract author/page name
            try:
                author_selectors = [
                    "h3 a",
                    "[data-testid='post_author_name']",
                    ".actor-name",
                    "strong a",
                    ".x1heor9g.x1qlqyl8.x1pd3egz.x1a2a7pz a"
                ]
                
                for selector in author_selectors:
                    try:
                        author_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        author_text = author_element.text.strip()
                        if author_text:
                            post_data['author'] = author_text
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not extract author: {e}")
            
            # Try to extract timestamp
            try:
                timestamp_selectors = [
                    "abbr",
                    "[data-testid='story-subtitle'] a",
                    ".timestampContent",
                    "time",
                    "a[role='link'][tabindex='0']"
                ]
                
                for selector in timestamp_selectors:
                    try:
                        timestamp_elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        for element in timestamp_elements:
                            # Check if element has time-related attributes or text
                            timestamp_text = element.get_attribute('title') or element.text
                            if timestamp_text and any(word in timestamp_text.lower() for word in ['ago', 'at', ':', 'am', 'pm', '2025', '2024']):
                                post_data['timestamp'] = timestamp_text.strip()
                                break
                        if post_data['timestamp']:
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Could not extract timestamp: {e}")
            
            # Extract hashtags from text
            if post_data['text']:
                hashtags = re.findall(r'#\w+', post_data['text'])
                post_data['hashtags'] = hashtags
            
            return post_data
            
        except Exception as e:
            logger.error(f"Error scraping post content from {url}: {e}")
            return {
                'text': '',
                'author': '',
                'timestamp': '',
                'likes': '',
                'comments': '',
                'shares': '',
                'hashtags': []
            }
    
    def scrape_post(self, url):
        """Scrape a single Facebook post and return all data"""
        logger.info(f"Scraping post: {url}")
        
        post_id = self.extract_post_id_from_url(url)
        content_data = self.scrape_post_content(url)
        
        return {
            'url': url,
            'post_id': post_id,
            'timestamp': content_data.get('timestamp', ''),
            'text': content_data.get('text', ''),
            'author': content_data.get('author', ''),
            'hashtags': ', '.join(content_data.get('hashtags', [])),
            'likes': content_data.get('likes', ''),
            'comments': content_data.get('comments', ''),
            'shares': content_data.get('shares', '')
        }
    
    def scrape_multiple_posts(self, urls):
        """Scrape multiple Facebook posts"""
        results = []
        
        for i, url in enumerate(urls, 1):
            logger.info(f"Processing post {i}/{len(urls)}")
            
            try:
                post_data = self.scrape_post(url.strip())
                results.append(post_data)
                
                # Add delay between requests to be respectful
                time.sleep(3)  # Facebook needs more delay
                
            except Exception as e:
                logger.error(f"Error processing {url}: {e}")
                # Add empty record for failed scrapes
                results.append({
                    'url': url,
                    'post_id': '',
                    'timestamp': '',
                    'text': '',
                    'author': '',
                    'hashtags': '',
                    'likes': '',
                    'comments': '',
                    'shares': ''
                })
        
        return results
    
    def save_to_csv(self, data, filename='facebook_posts.csv'):
        """Save scraped data to CSV file"""
        try:
            df = pd.DataFrame(data)
            df.to_csv(filename, index=False, encoding='utf-8')
            logger.info(f"Data saved to {filename}")
            return filename
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
    
    def close(self):
        """Close the WebDriver"""
        if hasattr(self, 'driver'):
            self.driver.quit()

def extract_urls_from_csv(csv_files):
    """Extract Facebook URLs from the existing CSV files"""
    urls = []
    
    for csv_file in csv_files:
        try:
            logger.info(f"Reading URLs from {csv_file}")
            df = pd.read_csv(csv_file)
            
            # Look for permalink column (Facebook URLs)
            if 'Permalink' in df.columns:
                file_urls = df['Permalink'].dropna().tolist()
                urls.extend(file_urls)
                logger.info(f"Found {len(file_urls)} URLs in {csv_file}")
            else:
                logger.warning(f"No 'Permalink' column found in {csv_file}")
                
        except Exception as e:
            logger.error(f"Error reading {csv_file}: {e}")
    
    # Remove duplicates while preserving order
    unique_urls = list(dict.fromkeys(urls))
    logger.info(f"Total unique URLs found: {len(unique_urls)}")
    
    return unique_urls

def main():
    """Main function to run the scraper"""
    # Facebook CSV files to process
    csv_files = ['FB_Feb1-Apr30.csv', 'FB_May1-Jun18.csv']
    
    scraper = FacebookPostScraper()
    
    try:
        # Extract URLs from CSV files
        urls = extract_urls_from_csv(csv_files)
        
        if not urls:
            print("No URLs found in CSV files!")
            return
        
        logger.info(f"Found {len(urls)} URLs to process")
        
        # Scrape all posts
        results = scraper.scrape_multiple_posts(urls)
        
        # Save to CSV
        filename = scraper.save_to_csv(results)
        
        if filename:
            print(f"\nScraping completed! Results saved to: {filename}")
            print(f"Processed {len(results)} posts")
            
            # Display summary
            df = pd.read_csv(filename)
            print(f"\nSummary:")
            print(f"- Total posts: {len(df)}")
            print(f"- Posts with text: {len(df[df['text'] != ''])}")
            print(f"- Posts with timestamps: {len(df[df['timestamp'] != ''])}")
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
