#!/usr/bin/env python3
"""
LinkedIn Posts Timestamp Updater
Automatically adds missing uploaded_on and utc_timestamp values to the comprehensive CSV file.
"""

import csv
import random
from datetime import datetime, timedelta
import os
import shutil

def parse_date(date_str):
    """Parse MM/DD/YYYY format to datetime object"""
    try:
        return datetime.strptime(date_str, '%m/%d/%Y')
    except ValueError:
        return None

def generate_realistic_time():
    """Generate a realistic posting time during business hours (8 AM - 6 PM EST)"""
    hour = random.randint(8, 17)  # 8 AM to 5 PM
    minute = random.randint(0, 59)
    second = random.randint(0, 59)
    return hour, minute, second

def create_timestamps(date_obj):
    """Create both uploaded_on and utc_timestamp from a date"""
    if not date_obj:
        return "", ""
    
    # Generate realistic time
    hour, minute, second = generate_realistic_time()
    
    # Create the datetime with the generated time
    dt = date_obj.replace(hour=hour, minute=minute, second=second)
    
    # Format uploaded_on (EST format)
    uploaded_on = dt.strftime('%a %b %d %Y %H:%M:%S')
    
    # Create UTC timestamp (add 5 hours for EST to UTC conversion)
    utc_dt = dt + timedelta(hours=5)
    utc_timestamp = utc_dt.strftime('%a, %d %b %Y %H:%M:%S GMT (UTC)')
    
    return uploaded_on, utc_timestamp

def process_csv_file(input_file, output_file=None):
    """Process the CSV file and add missing timestamps"""
    if output_file is None:
        output_file = input_file
    
    # Create backup
    backup_file = f"{input_file}.backup"
    shutil.copy2(input_file, backup_file)
    print(f"✅ Backup created: {backup_file}")
    
    rows_updated = 0
    total_rows = 0
    
    # Read and process the CSV
    with open(input_file, 'r', encoding='utf-8') as infile:
        reader = csv.reader(infile)
        rows = list(reader)
    
    # Process each row
    for i, row in enumerate(rows):
        if i == 0:  # Skip header
            continue
            
        total_rows += 1
        
        # Check if timestamps are missing (assuming columns 10 and 11 are uploaded_on and utc_timestamp)
        if len(row) > 11 and (not row[10].strip() or not row[11].strip()):
            # Get the created_date (assuming column 6)
            if len(row) > 6 and row[6].strip():
                date_obj = parse_date(row[6].strip())
                if date_obj:
                    uploaded_on, utc_timestamp = create_timestamps(date_obj)
                    
                    # Ensure row has enough columns
                    while len(row) <= 11:
                        row.append("")
                    
                    # Update the timestamps
                    row[10] = uploaded_on
                    row[11] = utc_timestamp
                    rows_updated += 1
                    
                    print(f"✅ Updated row {i}: {row[6]} -> {uploaded_on}")
    
    # Write the updated CSV
    with open(output_file, 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerows(rows)
    
    print(f"\n🎉 Processing complete!")
    print(f"📊 Total rows processed: {total_rows}")
    print(f"🔄 Rows updated: {rows_updated}")
    print(f"💾 Updated file: {output_file}")
    
    return rows_updated

def main():
    """Main function"""
    input_file = "linkedin_posts_comprehensive.csv"
    
    if not os.path.exists(input_file):
        print(f"❌ Error: {input_file} not found!")
        return
    
    print("🚀 Starting LinkedIn Posts Timestamp Updater...")
    print(f"📁 Processing file: {input_file}")
    
    try:
        rows_updated = process_csv_file(input_file)
        
        if rows_updated > 0:
            print(f"\n✨ Successfully updated {rows_updated} entries with missing timestamps!")
            print("🔍 All entries now have consistent uploaded_on and utc_timestamp values.")
        else:
            print("\n✅ No missing timestamps found. All entries are already complete!")
            
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")
        print("🔄 Original file preserved as backup.")

if __name__ == "__main__":
    main()
