#!/usr/bin/env python3
"""
Merge Facebook scraped data with existing CSV files
Combines the scraped Facebook post data with the original CSV analytics data.
"""

import pandas as pd
import logging
from urllib.parse import urlparse

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def normalize_url(url):
    """Normalize Facebook URLs for better matching"""
    if pd.isna(url) or not url:
        return ""
    
    try:
        # Remove query parameters and fragments for better matching
        parsed = urlparse(str(url))
        normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
        return normalized.rstrip('/')
    except:
        return str(url).strip()

def merge_facebook_data():
    """Merge scraped Facebook data with original CSV files"""
    
    # File paths
    scraped_file = 'facebook_posts.csv'
    original_files = ['FB_Feb1-Apr30.csv', 'FB_May1-Jun18.csv']
    
    try:
        # Read scraped data
        logger.info(f"Reading scraped data from {scraped_file}")
        scraped_df = pd.read_csv(scraped_file)
        logger.info(f"Scraped data shape: {scraped_df.shape}")
        
        # Normalize URLs in scraped data
        scraped_df['url_normalized'] = scraped_df['url'].apply(normalize_url)
        
        # Process each original CSV file
        merged_data = []
        
        for original_file in original_files:
            try:
                logger.info(f"Processing {original_file}")
                original_df = pd.read_csv(original_file)
                logger.info(f"Original data shape: {original_df.shape}")
                
                # Normalize URLs in original data
                if 'Permalink' in original_df.columns:
                    original_df['url_normalized'] = original_df['Permalink'].apply(normalize_url)
                    
                    # Merge data based on normalized URLs
                    merged = pd.merge(
                        original_df,
                        scraped_df[['url_normalized', 'post_id', 'timestamp', 'text', 'author', 'hashtags']],
                        on='url_normalized',
                        how='left'
                    )
                    
                    # Drop the temporary normalized URL column
                    merged = merged.drop('url_normalized', axis=1)
                    
                    # Add source file info
                    merged['source_file'] = original_file
                    
                    merged_data.append(merged)
                    logger.info(f"Merged data shape for {original_file}: {merged.shape}")
                    
                else:
                    logger.warning(f"No 'Permalink' column found in {original_file}")
                    
            except Exception as e:
                logger.error(f"Error processing {original_file}: {e}")
        
        if merged_data:
            # Combine all merged data
            final_df = pd.concat(merged_data, ignore_index=True)
            
            # Save merged data
            output_file = 'facebook_posts_comprehensive.csv'
            final_df.to_csv(output_file, index=False, encoding='utf-8')
            
            logger.info(f"Merged data saved to {output_file}")
            print(f"Successfully merged Facebook data!")
            print(f"Output file: {output_file}")
            print(f"Total records: {len(final_df)}")
            
            # Display summary statistics
            print(f"\nSummary:")
            print(f"- Total posts: {len(final_df)}")
            print(f"- Posts with scraped text: {len(final_df[final_df['text'].notna() & (final_df['text'] != '')])}")
            print(f"- Posts with scraped timestamps: {len(final_df[final_df['timestamp'].notna() & (final_df['timestamp'] != '')])}")
            print(f"- Posts with hashtags: {len(final_df[final_df['hashtags'].notna() & (final_df['hashtags'] != '')])}")
            
            # Show column names
            print(f"\nColumns in merged file:")
            for i, col in enumerate(final_df.columns, 1):
                print(f"{i:2d}. {col}")
            
            # Show sample of merged data
            print(f"\nSample of merged data:")
            sample_cols = ['Title', 'Permalink', 'text', 'hashtags', 'Views', 'Reach']
            available_cols = [col for col in sample_cols if col in final_df.columns]
            if available_cols:
                print(final_df[available_cols].head(3).to_string(index=False))
            
        else:
            print("No data was successfully merged!")
            
    except FileNotFoundError as e:
        print(f"File not found: {e}")
        print("Make sure you have run the Facebook scraper first to generate facebook_posts.csv")
    except Exception as e:
        logger.error(f"Error in merge process: {e}")

if __name__ == "__main__":
    merge_facebook_data()
