#!/usr/bin/env python3
"""
Test Facebook Scraper
Tests the Facebook scraper with a few sample URLs to verify it's working correctly.
"""

import pandas as pd
from facebook_scraper import FacebookPostScraper
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_facebook_scraper():
    """Test the Facebook scraper with sample URLs"""
    
    # Get a few sample URLs from the CSV files
    sample_urls = []
    
    try:
        # Read a few URLs from the first CSV file
        df = pd.read_csv('FB_Feb1-Apr30.csv')
        if 'Permalink' in df.columns:
            sample_urls = df['Permalink'].dropna().head(3).tolist()
            print(f"Testing with {len(sample_urls)} sample URLs:")
            for i, url in enumerate(sample_urls, 1):
                print(f"{i}. {url}")
        else:
            print("No 'Permalink' column found in CSV file")
            return
            
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return
    
    if not sample_urls:
        print("No URLs found to test")
        return
    
    # Initialize scraper
    scraper = FacebookPostScraper()
    
    try:
        print(f"\nStarting test scraping...")
        
        # Test scraping
        results = scraper.scrape_multiple_posts(sample_urls)
        
        # Display results
        print(f"\nTest Results:")
        print(f"Processed {len(results)} posts")
        
        for i, result in enumerate(results, 1):
            print(f"\n--- Post {i} ---")
            print(f"URL: {result['url'][:80]}...")
            print(f"Post ID: {result['post_id']}")
            print(f"Author: {result['author']}")
            print(f"Timestamp: {result['timestamp']}")
            print(f"Text: {result['text'][:100]}..." if result['text'] else "Text: (not found)")
            print(f"Hashtags: {result['hashtags']}")
        
        # Save test results
        test_filename = 'facebook_posts_test.csv'
        scraper.save_to_csv(results, test_filename)
        print(f"\nTest results saved to: {test_filename}")
        
    except Exception as e:
        logger.error(f"Error during testing: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    test_facebook_scraper()
