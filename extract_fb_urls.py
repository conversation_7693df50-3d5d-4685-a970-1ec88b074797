#!/usr/bin/env python3
"""
Extract Facebook URLs from CSV files
Reads the FB CSV files and extracts all unique Facebook URLs to a text file.
"""

import pandas as pd
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_urls_from_csv_files():
    """Extract Facebook URLs from the CSV files and save to text file"""
    
    # Facebook CSV files to process
    csv_files = ['FB_Feb1-Apr30.csv', 'FB_May1-Jun18.csv']
    
    all_urls = []
    
    for csv_file in csv_files:
        try:
            logger.info(f"Reading URLs from {csv_file}")
            df = pd.read_csv(csv_file)
            
            # Display column names for debugging
            logger.info(f"Columns in {csv_file}: {list(df.columns)}")
            
            # Look for permalink column (Facebook URLs)
            if 'Permalink' in df.columns:
                file_urls = df['Permalink'].dropna().tolist()
                all_urls.extend(file_urls)
                logger.info(f"Found {len(file_urls)} URLs in {csv_file}")
            else:
                logger.warning(f"No 'Permalink' column found in {csv_file}")
                
        except Exception as e:
            logger.error(f"Error reading {csv_file}: {e}")
    
    # Remove duplicates while preserving order
    unique_urls = list(dict.fromkeys(all_urls))
    logger.info(f"Total unique URLs found: {len(unique_urls)}")
    
    # Save URLs to text file
    try:
        with open('facebook_urls.txt', 'w') as f:
            for url in unique_urls:
                f.write(f"{url}\n")
        
        logger.info(f"URLs saved to facebook_urls.txt")
        print(f"Successfully extracted {len(unique_urls)} unique Facebook URLs")
        print("URLs saved to: facebook_urls.txt")
        
        # Display first few URLs as sample
        print("\nSample URLs:")
        for i, url in enumerate(unique_urls[:5]):
            print(f"{i+1}. {url}")
        
        if len(unique_urls) > 5:
            print(f"... and {len(unique_urls) - 5} more URLs")
            
    except Exception as e:
        logger.error(f"Error saving URLs to file: {e}")

if __name__ == "__main__":
    extract_urls_from_csv_files()
