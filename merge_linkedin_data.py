import pandas as pd
import re
from urllib.parse import urlparse, parse_qs

def extract_post_id_from_url(url):
    """Extract post ID from LinkedIn URL"""
    if pd.isna(url) or not url:
        return None
    
    # Try to extract from activity ID in URL
    activity_match = re.search(r'activity-(\d+)', url)
    if activity_match:
        return activity_match.group(1)
    
    # Try to extract from urn:li:activity format
    urn_match = re.search(r'urn:li:activity:(\d+)', url)
    if urn_match:
        return urn_match.group(1)
    
    return None

def clean_text(text):
    """Clean and normalize text content"""
    if pd.isna(text):
        return ""
    
    # Remove extra whitespace and normalize line breaks
    text = re.sub(r'\s+', ' ', str(text).strip())
    return text

def merge_linkedin_files():
    """Merge the two LinkedIn CSV files"""

    # Read both files
    print("Reading original linkedin_posts.csv...")
    original_df = pd.read_csv('linkedin_posts.csv')

    print("Reading comprehensive LinkedIn_DNI_POST.csv...")
    # Read the comprehensive CSV file with all metrics
    comprehensive_df = pd.read_csv('LinkedIn_DNI_POST.csv', skiprows=1)

    # The actual column names are in what is now the first row
    if 'Post title' in str(comprehensive_df.iloc[0, 0]):
        # Get the actual column names from the first row
        new_columns = comprehensive_df.iloc[0].tolist()
        comprehensive_df.columns = new_columns
        # Skip the first row (which is now our header)
        comprehensive_df = comprehensive_df.iloc[1:].reset_index(drop=True)

    # Rename columns in comprehensive_df to standardize while preserving all important metrics
    comprehensive_df = comprehensive_df.rename(columns={
        'Post title': 'text',
        'Post link': 'url',
        'Post type': 'post_type',
        'Campaign name': 'campaign_name',
        'Posted by': 'author',
        'Created date': 'created_date',
        'Campaign start date': 'campaign_start_date',
        'Campaign end date': 'campaign_end_date',
        'Audience': 'audience',
        'Impressions': 'impressions',
        'Views': 'views',
        'Offsite Views': 'offsite_views',
        'Clicks': 'clicks',
        'Click through rate (CTR)': 'ctr',
        'Likes': 'likes',
        'Comments': 'comments',
        'Reposts': 'shares',
        'Follows': 'follows',
        'Engagement rate': 'engagement_rate',
        'Content Type': 'content_type'
    })
    
    # Extract post IDs from URLs in comprehensive data
    comprehensive_df['post_id'] = comprehensive_df['url'].apply(extract_post_id_from_url)

    # Clean text content
    comprehensive_df['text'] = comprehensive_df['text'].apply(clean_text)
    original_df['text'] = original_df['text'].apply(clean_text)

    # Create a mapping from original data
    original_mapping = {}
    for _, row in original_df.iterrows():
        post_id = str(row['post_id']) if pd.notna(row['post_id']) else None
        if post_id:
            original_mapping[post_id] = {
                'url': row['url'],
                'uploaded_on': row['uploaded_on'],
                'utc_timestamp': row['utc_timestamp'],
                'hashtags': row['hashtags']
            }

    # Merge data
    merged_rows = []

    for _, comp_row in comprehensive_df.iterrows():
        post_id = str(comp_row['post_id']) if pd.notna(comp_row['post_id']) else None

        # Create merged row with all comprehensive metrics
        merged_row = {
            'url': comp_row['url'],
            'post_id': comp_row['post_id'],
            'text': comp_row['text'],
            'author': comp_row['author'],
            'post_type': comp_row.get('post_type', ''),
            'campaign_name': comp_row.get('campaign_name', ''),
            'created_date': comp_row['created_date'],
            'campaign_start_date': comp_row.get('campaign_start_date', ''),
            'campaign_end_date': comp_row.get('campaign_end_date', ''),
            'audience': comp_row.get('audience', ''),
            'impressions': comp_row.get('impressions', ''),
            'views': comp_row.get('views', ''),
            'offsite_views': comp_row.get('offsite_views', ''),
            'clicks': comp_row.get('clicks', ''),
            'ctr': comp_row.get('ctr', ''),
            'likes': comp_row.get('likes', ''),
            'comments': comp_row.get('comments', ''),
            'shares': comp_row.get('shares', ''),
            'follows': comp_row.get('follows', ''),
            'engagement_rate': comp_row.get('engagement_rate', ''),
            'content_type': comp_row.get('content_type', ''),
            'uploaded_on': '',
            'utc_timestamp': '',
            'hashtags': ''
        }
        
        # Add original data if available
        if post_id and post_id in original_mapping:
            original_data = original_mapping[post_id]
            merged_row.update({
                'uploaded_on': original_data['uploaded_on'],
                'utc_timestamp': original_data['utc_timestamp'],
                'hashtags': original_data['hashtags']
            })
        
        merged_rows.append(merged_row)
    
    # Create final dataframe
    merged_df = pd.DataFrame(merged_rows)
    
    # Reorder columns for better readability with all important metrics
    column_order = [
        'url', 'post_id', 'text', 'author', 'post_type', 'campaign_name', 'created_date',
        'campaign_start_date', 'campaign_end_date', 'audience', 'uploaded_on', 'utc_timestamp',
        'hashtags', 'impressions', 'views', 'offsite_views', 'clicks', 'ctr', 'likes',
        'comments', 'shares', 'follows', 'engagement_rate', 'content_type'
    ]

    # Only include columns that exist
    available_columns = [col for col in column_order if col in merged_df.columns]
    merged_df = merged_df[available_columns]

    # Save merged file
    output_file = 'linkedin_posts_comprehensive.csv'
    merged_df.to_csv(output_file, index=False)

    print(f"\nMerge completed!")
    print(f"Original file: {len(original_df)} posts")
    print(f"Comprehensive file: {len(comprehensive_df)} posts")
    print(f"Merged file: {len(merged_df)} posts")
    print(f"Output saved to: {output_file}")

    # Show sample of merged data with key metrics
    print(f"\nSample of merged data:")
    sample_cols = ['text', 'post_type', 'audience', 'impressions', 'likes', 'follows', 'engagement_rate']
    available_sample_cols = [col for col in sample_cols if col in merged_df.columns]
    print(merged_df[available_sample_cols].head(3))
    
    return merged_df

if __name__ == "__main__":
    merged_df = merge_linkedin_files()
